#!/usr/bin/env python3
"""
Test script for the prompt enhancer functionality.
"""

import json
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from prompt_enhancer.graph.builder import build_graph


def test_prompt_enhancer():
    """Test the prompt enhancer with a sample prompt."""
    
    # Build the graph
    graph = build_graph()
    
    # Test prompt
    test_prompt = "Write about AI"
    
    print(f"Testing prompt enhancer with: '{test_prompt}'")
    
    try:
        # Invoke the graph
        result = graph.invoke({
            "prompt": test_prompt,
            "context": "",
            "task_type": "general"
        })
        
        print("Result:", result)
        
        # Try to parse the output
        if "output" in result:
            try:
                parsed_output = json.loads(result["output"])
                print("Parsed output:")
                print(json.dumps(parsed_output, indent=2))
            except json.JSONDecodeError as e:
                print(f"Failed to parse output as JSON: {e}")
                print("Raw output:", result["output"])
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_prompt_enhancer()
