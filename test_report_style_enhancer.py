#!/usr/bin/env python3
"""
Test script for the prompt enhancer functionality with report style.
"""

import json
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from prompt_enhancer.graph.builder import build_graph
from config.report_style import ReportStyle


def test_prompt_enhancer_with_report_style():
    """Test the prompt enhancer with different report styles."""
    
    # Build the graph
    graph = build_graph()
    
    # Test prompt
    test_prompt = "Write about AI"
    
    # Test with different report styles
    styles = [ReportStyle.ACADEMIC, ReportStyle.NEWS, ReportStyle.SOCIAL_MEDIA]
    
    for style in styles:
        print(f"\n{'='*50}")
        print(f"Testing with report style: {style.value}")
        print(f"{'='*50}")
        print(f"Original prompt: '{test_prompt}'")
        
        try:
            # Invoke the graph
            result = graph.invoke({
                "prompt": test_prompt,
                "context": "",
                "report_style": style
            })
            
            print(f"\nEnhanced prompt:")
            print("-" * 30)
            print(result.get("output", "No output"))
            
        except Exception as e:
            print(f"Error with style {style.value}: {e}")
            import traceback
            traceback.print_exc()


if __name__ == "__main__":
    test_prompt_enhancer_with_report_style()
