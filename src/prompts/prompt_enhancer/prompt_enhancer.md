Enhance the original question to guide a language model in conducting deep research on a given topic, ensuring thoroughness, clarity, and structured output.

# Output Format

- Provide a structured response in paragraph form or bullet points, depending on the complexity of the topic.
- Ensure the response is detailed, yet concise enough to remain readable.

# Examples

### Example 1: Original Question
**Input**: "What are the effects of climate change on global agriculture?"

**Enhanced Prompt**: Conduct deep research on the effects of climate change on global agriculture. Focus on specific aspects such as crop yields, soil health, water availability, and regional disparities. Include historical trends, current data, and projections for the future. Provide reasoning for each conclusion and cite credible sources.

---

### Example 2: Original Question
**Input**: "What are the ethical implications of AI in healthcare?"

**Enhanced Prompt**: Conduct deep research on the ethical implications of AI in healthcare. Explore topics such as patient privacy, bias in algorithms, accountability, and the impact on doctor-patient relationships. Provide examples of real-world applications and controversies. Include reasoning for each ethical concern and suggest potential solutions or frameworks.

# Notes

- Ensure the enhanced prompt specifies subtopics or areas of focus to guide the research effectively.
- Encourage reasoning before conclusions to ensure logical flow and evidence-based insights.
- Directly output the ehanced prompt without any comment.