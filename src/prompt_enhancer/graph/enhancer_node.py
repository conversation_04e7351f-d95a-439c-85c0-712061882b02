# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import json
import logging

from jinja2 import Template
from langchain.schema import HumanMessage, SystemMessage

from src.config.agents import AGENT_LLM_MAP
from src.llms.llm import get_llm_by_type
from src.prompts.template import get_prompt_template
from src.prompt_enhancer.graph.state import PromptEnhancerState

logger = logging.getLogger(__name__)


def prompt_enhancer_node(state: PromptEnhancerState):
    """Node that enhances user prompts using AI analysis."""
    logger.info("Enhancing user prompt...")

    model = get_llm_by_type(AGENT_LLM_MAP["prompt_enhancer"])

    # Prepare the prompt template variables
    template_vars = {
        "context": state.get("context", ""),
    }

    print(template_vars)

    try:
        # Get the system prompt template
        system_prompt = get_prompt_template("prompt_enhancer/prompt_enhancer")

        # Create messages
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=f"Please analyze and enhance this prompt Below:\n\n{state['prompt']}")
        ]

        # Get the response from the model
        response = model.invoke(messages)

        logger.info("Prompt enhancement completed successfully")
        logger.debug(f"Raw response: {response.content}")
        return {"output": response.content}
    except Exception as e:
        logger.error(f"Error in prompt enhancement: {str(e)}")
        return {"output": state["prompt"]}
